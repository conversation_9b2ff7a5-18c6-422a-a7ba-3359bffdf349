#!/usr/bin/env python3
"""
HVI-RF-DETR 论文实验推理系统
基于研究思路_夜间实时检测.md的科学架构实现

论文实验功能：
1. 读取训练时的性能指标（mAP, FPS, Loss）
2. 运行全局数据集推理
3. 生成论文级别的实验日志和报告
4. 对比训练与推理性能
5. 生成实验表格和图表
"""

import torch
import torch.nn as nn
import numpy as np
import json
import time
import logging
import os
from pathlib import Path
from torch.utils.tensorboard import SummaryWriter
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 导入基础推理系统
import sys
sys.path.append(str(Path(__file__).parent))
from global_inference_system import GlobalInferenceSystem, GlobalInferenceConfig, HVIRFDETRInferenceModel

class PaperExperimentConfig(GlobalInferenceConfig):
    """论文实验配置 - 扩展基础配置"""

    def __init__(self):
        super().__init__()

        # 训练指标路径
        self.training_metrics_dir = "unified_runs/hvi_rf_detr_28k_20250624_162451/metric"
        self.training_history_path = "unified_runs/hvi_rf_detr_28k_20250624_162451/training_history.json"

        # 论文实验配置
        self.experiment_name = "HVI-RF-DETR夜间实时检测系统"
        self.paper_output_dir = "paper_experiments"
        self.generate_latex_tables = True
        self.generate_performance_plots = True

        # 实验参数
        self.dataset_name = "BDD100K夜间子集"
        self.model_architecture = "HVI-RF-DETR"
        self.training_epochs = 50
        self.training_dataset_size = 28028
        self.batch_size_training = 32

        # 性能基准
        self.target_metrics = {
            'mAP_threshold': 0.50,  # 研究目标 mAP@0.5:0.95 ≥50%
            'fps_threshold': 30.0,  # 研究目标 ≥30 FPS
            'latency_threshold': 33.33  # 33.33ms = 30 FPS
        }

class PaperExperimentSystem(GlobalInferenceSystem):
    """论文实验系统 - 扩展基础推理系统"""

    def __init__(self, config: PaperExperimentConfig):
        super().__init__(config)
        self.paper_config = config

        # 创建论文实验目录
        self.paper_dir = Path(config.paper_output_dir)
        self.paper_dir.mkdir(parents=True, exist_ok=True)

        # 训练指标
        self.training_metrics = {}
        self.load_training_metrics()

        # 实验报告
        self.experiment_report = {
            'experiment_info': {},
            'training_performance': {},
            'inference_performance': {},
            'comparison_analysis': {},
            'conclusions': {}
        }

        self.logger.info(f"📊 论文实验系统初始化完成")
        self.logger.info(f"📁 论文输出目录: {self.paper_dir}")

    def load_training_metrics(self):
        """加载训练时的性能指标"""
        try:
            self.logger.info("📊 加载训练性能指标...")

            # 加载训练历史
            history_path = Path(self.paper_config.training_history_path)
            if history_path.exists():
                with open(history_path, 'r', encoding='utf-8') as f:
                    training_history = json.load(f)

                self.training_metrics['history'] = training_history
                self.logger.info(f"✅ 训练历史加载成功: {len(training_history['losses'])} epochs")

            # 加载详细指标文件
            metrics_dir = Path(self.paper_config.training_metrics_dir)
            if metrics_dir.exists():
                # mAP指标
                map_file = metrics_dir / "run-hvi_rf_detr_28k_20250624_162451_tensorboard-tag-Epoch_mAP.json"
                if map_file.exists():
                    with open(map_file, 'r') as f:
                        map_data = json.load(f)
                    self.training_metrics['mAP_timeline'] = map_data

                # FPS指标
                fps_file = metrics_dir / "run-hvi_rf_detr_28k_20250624_162451_tensorboard-tag-Epoch_FPS.json"
                if fps_file.exists():
                    with open(fps_file, 'r') as f:
                        fps_data = json.load(f)
                    self.training_metrics['FPS_timeline'] = fps_data

                # Loss指标
                loss_file = metrics_dir / "run-hvi_rf_detr_28k_20250624_162451_tensorboard-tag-Epoch_Loss.json"
                if loss_file.exists():
                    with open(loss_file, 'r') as f:
                        loss_data = json.load(f)
                    self.training_metrics['Loss_timeline'] = loss_data

                self.logger.info("✅ 详细训练指标加载成功")

            # 计算训练性能统计
            self._calculate_training_statistics()

        except Exception as e:
            self.logger.error(f"❌ 训练指标加载失败: {e}")

    def _calculate_training_statistics(self):
        """计算训练性能统计"""
        if 'history' not in self.training_metrics:
            return

        history = self.training_metrics['history']

        # 最终性能
        final_mAP = history['mAPs'][-1] if history['mAPs'] else 0
        final_fps = history['fps_values'][-1] if history['fps_values'] else 0
        final_loss = history['losses'][-1] if history['losses'] else 0

        # 最佳性能
        best_mAP = max(history['mAPs']) if history['mAPs'] else 0
        best_fps = max(history['fps_values']) if history['fps_values'] else 0
        min_loss = min(history['losses']) if history['losses'] else 0

        # 平均性能（最后10个epoch）
        last_10_epochs = 10
        avg_mAP_final = np.mean(history['mAPs'][-last_10_epochs:]) if len(history['mAPs']) >= last_10_epochs else final_mAP
        avg_fps_final = np.mean(history['fps_values'][-last_10_epochs:]) if len(history['fps_values']) >= last_10_epochs else final_fps

        self.training_metrics['statistics'] = {
            'final_performance': {
                'mAP': final_mAP,
                'fps': final_fps,
                'loss': final_loss
            },
            'best_performance': {
                'mAP': best_mAP,
                'fps': best_fps,
                'loss': min_loss
            },
            'average_final_performance': {
                'mAP': avg_mAP_final,
                'fps': avg_fps_final
            },
            'training_epochs': len(history['losses']),
            'total_iterations': history.get('total_iterations', 0),
            'dataset_size': history.get('dataset_info', {}).get('dataset_size', 0)
        }

        self.logger.info(f"📊 训练性能统计:")
        self.logger.info(f"   🎯 最终mAP: {final_mAP:.3f}")
        self.logger.info(f"   🚀 最终FPS: {final_fps:.1f}")
        self.logger.info(f"   📉 最终Loss: {final_loss:.4f}")
        self.logger.info(f"   🏆 最佳mAP: {best_mAP:.3f}")
        self.logger.info(f"   ⚡ 最佳FPS: {best_fps:.1f}")

    def run_paper_experiment(self):
        """运行论文实验 - 主要执行函数"""
        self.logger.info("🎓 开始论文实验...")

        # 1. 记录实验信息
        self._record_experiment_info()

        # 2. 运行推理实验
        self.logger.info("\n🔬 运行推理实验...")
        super().run_global_inference()

        # 3. 分析实验结果
        self._analyze_experiment_results()

        # 4. 生成论文报告
        self._generate_paper_report()

        # 5. 生成可视化图表
        if self.paper_config.generate_performance_plots:
            self._generate_performance_plots()

        # 6. 生成LaTeX表格
        if self.paper_config.generate_latex_tables:
            self._generate_latex_tables()

        self.logger.info("🎓 论文实验完成！")

    def _record_experiment_info(self):
        """记录实验基本信息"""
        self.experiment_report['experiment_info'] = {
            'experiment_name': self.paper_config.experiment_name,
            'dataset': self.paper_config.dataset_name,
            'model_architecture': self.paper_config.model_architecture,
            'training_epochs': self.paper_config.training_epochs,
            'training_dataset_size': self.paper_config.training_dataset_size,
            'batch_size': self.paper_config.batch_size_training,
            'target_metrics': self.paper_config.target_metrics,
            'experiment_timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
            'device': str(self.device),
            'pytorch_version': torch.__version__
        }

    def _analyze_experiment_results(self):
        """分析实验结果"""
        # 训练性能分析
        if 'statistics' in self.training_metrics:
            training_stats = self.training_metrics['statistics']
            self.experiment_report['training_performance'] = {
                'final_mAP': training_stats['final_performance']['mAP'],
                'final_fps': training_stats['final_performance']['fps'],
                'final_loss': training_stats['final_performance']['loss'],
                'best_mAP': training_stats['best_performance']['mAP'],
                'best_fps': training_stats['best_performance']['fps'],
                'convergence_epochs': training_stats['training_epochs'],
                'total_training_iterations': training_stats['total_iterations']
            }

        # 推理性能分析
        inference_stats = self.inference_stats
        if inference_stats['fps_values']:
            avg_inference_fps = np.mean(inference_stats['fps_values'])
            avg_inference_latency = np.mean(inference_stats['latency_values'])
            avg_memory = np.mean(inference_stats['memory_usage'])

            self.experiment_report['inference_performance'] = {
                'total_images_processed': inference_stats['total_images'],
                'total_detections': inference_stats['total_detections'],
                'average_fps': avg_inference_fps,
                'average_latency_ms': avg_inference_latency,
                'average_memory_mb': avg_memory,
                'fps_std': np.std(inference_stats['fps_values']),
                'latency_std': np.std(inference_stats['latency_values']),
                'split_performance': inference_stats['split_stats']
            }

        # 性能对比分析
        self._compare_training_inference_performance()

    def _compare_training_inference_performance(self):
        """对比训练与推理性能"""
        comparison = {}

        if 'training_performance' in self.experiment_report and 'inference_performance' in self.experiment_report:
            training_perf = self.experiment_report['training_performance']
            inference_perf = self.experiment_report['inference_performance']

            # FPS对比
            training_fps = training_perf.get('final_fps', 0)
            inference_fps = inference_perf.get('average_fps', 0)

            comparison['fps_comparison'] = {
                'training_fps': training_fps,
                'inference_fps': inference_fps,
                'fps_difference': inference_fps - training_fps,
                'fps_ratio': inference_fps / training_fps if training_fps > 0 else 0
            }

            # 目标达成分析
            target_metrics = self.paper_config.target_metrics
            comparison['target_achievement'] = {
                'mAP_target': target_metrics['mAP_threshold'],
                'mAP_achieved': training_perf.get('best_mAP', 0),
                'mAP_achievement_rate': (training_perf.get('best_mAP', 0) / target_metrics['mAP_threshold']) * 100,
                'fps_target': target_metrics['fps_threshold'],
                'fps_achieved': inference_fps,
                'fps_achievement_rate': (inference_fps / target_metrics['fps_threshold']) * 100,
                'latency_target': target_metrics['latency_threshold'],
                'latency_achieved': inference_perf.get('average_latency_ms', 0),
                'latency_achievement': inference_perf.get('average_latency_ms', 0) <= target_metrics['latency_threshold']
            }

        self.experiment_report['comparison_analysis'] = comparison

    def _generate_paper_report(self):
        """生成论文级别的实验报告"""
        self.logger.info("📝 生成论文实验报告...")

        report_path = self.paper_dir / "experiment_report.md"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(self._format_paper_report())

        # 同时生成JSON格式的详细数据
        json_path = self.paper_dir / "experiment_data.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(self.experiment_report, f, indent=2, ensure_ascii=False)

        self.logger.info(f"✅ 论文报告已生成: {report_path}")
        self.logger.info(f"✅ 实验数据已保存: {json_path}")

    def _format_paper_report(self) -> str:
        """格式化论文报告"""
        report = []

        # 标题和基本信息
        exp_info = self.experiment_report.get('experiment_info', {})
        report.append(f"# {exp_info.get('experiment_name', 'HVI-RF-DETR实验报告')}")
        report.append(f"\n**实验时间**: {exp_info.get('experiment_timestamp', 'N/A')}")
        report.append(f"**数据集**: {exp_info.get('dataset', 'N/A')}")
        report.append(f"**模型架构**: {exp_info.get('model_architecture', 'N/A')}")
        report.append(f"**训练轮数**: {exp_info.get('training_epochs', 'N/A')}")
        report.append(f"**数据集大小**: {exp_info.get('training_dataset_size', 'N/A'):,}")
        report.append(f"**设备**: {exp_info.get('device', 'N/A')}")

        # 训练性能
        training_perf = self.experiment_report.get('training_performance', {})
        if training_perf:
            report.append("\n## 训练性能")
            report.append(f"- **最终mAP**: {training_perf.get('final_mAP', 0):.3f}")
            report.append(f"- **最佳mAP**: {training_perf.get('best_mAP', 0):.3f}")
            report.append(f"- **最终FPS**: {training_perf.get('final_fps', 0):.1f}")
            report.append(f"- **最佳FPS**: {training_perf.get('best_fps', 0):.1f}")
            report.append(f"- **最终Loss**: {training_perf.get('final_loss', 0):.4f}")
            report.append(f"- **收敛轮数**: {training_perf.get('convergence_epochs', 0)}")
            report.append(f"- **总训练迭代**: {training_perf.get('total_training_iterations', 0):,}")

        # 推理性能
        inference_perf = self.experiment_report.get('inference_performance', {})
        if inference_perf:
            report.append("\n## 推理性能")
            report.append(f"- **处理图片总数**: {inference_perf.get('total_images_processed', 0):,}")
            report.append(f"- **检测总数**: {inference_perf.get('total_detections', 0):,}")
            report.append(f"- **平均FPS**: {inference_perf.get('average_fps', 0):.1f} ± {inference_perf.get('fps_std', 0):.1f}")
            report.append(f"- **平均延迟**: {inference_perf.get('average_latency_ms', 0):.1f} ± {inference_perf.get('latency_std', 0):.1f} ms")
            report.append(f"- **平均内存使用**: {inference_perf.get('average_memory_mb', 0):.1f} MB")

            # 分割性能
            split_stats = inference_perf.get('split_performance', {})
            if split_stats:
                report.append("\n### 各数据分割性能")
                for split, stats in split_stats.items():
                    report.append(f"- **{split}**: {stats['images']:,}张图片, "
                                f"FPS: {stats['avg_fps']:.1f}, "
                                f"延迟: {stats['avg_latency']:.1f}ms, "
                                f"检测: {stats['detections']:,}")

        # 性能对比分析
        comparison = self.experiment_report.get('comparison_analysis', {})
        if comparison:
            report.append("\n## 性能对比分析")

            fps_comp = comparison.get('fps_comparison', {})
            if fps_comp:
                report.append(f"- **训练FPS**: {fps_comp.get('training_fps', 0):.1f}")
                report.append(f"- **推理FPS**: {fps_comp.get('inference_fps', 0):.1f}")
                report.append(f"- **FPS差异**: {fps_comp.get('fps_difference', 0):+.1f}")
                report.append(f"- **FPS比率**: {fps_comp.get('fps_ratio', 0):.2f}")

            target_achievement = comparison.get('target_achievement', {})
            if target_achievement:
                report.append("\n### 目标达成情况")
                report.append(f"- **mAP目标**: {target_achievement.get('mAP_target', 0):.1f}%")
                report.append(f"- **mAP达成**: {target_achievement.get('mAP_achieved', 0):.3f} "
                            f"({target_achievement.get('mAP_achievement_rate', 0):.1f}%)")
                report.append(f"- **FPS目标**: {target_achievement.get('fps_target', 0):.1f}")
                report.append(f"- **FPS达成**: {target_achievement.get('fps_achieved', 0):.1f} "
                            f"({target_achievement.get('fps_achievement_rate', 0):.1f}%)")
                report.append(f"- **延迟目标**: ≤{target_achievement.get('latency_target', 0):.1f}ms")
                report.append(f"- **延迟达成**: {target_achievement.get('latency_achieved', 0):.1f}ms "
                            f"({'✅' if target_achievement.get('latency_achievement', False) else '❌'})")

        # 结论
        report.append("\n## 实验结论")

        # 自动生成结论
        conclusions = []
        if training_perf and inference_perf:
            final_mAP = training_perf.get('best_mAP', 0)
            avg_fps = inference_perf.get('average_fps', 0)
            avg_latency = inference_perf.get('average_latency_ms', 0)

            if final_mAP >= 0.5:
                conclusions.append(f"✅ 模型在夜间数据集上达到了{final_mAP:.1%}的mAP，超过了50%的目标阈值")
            else:
                conclusions.append(f"⚠️ 模型mAP为{final_mAP:.1%}，未达到50%的目标阈值")

            if avg_fps >= 30.0:
                conclusions.append(f"✅ 推理速度达到{avg_fps:.1f} FPS，满足实时性要求（≥30 FPS）")
            else:
                conclusions.append(f"⚠️ 推理速度为{avg_fps:.1f} FPS，未达到实时性要求（≥30 FPS）")

            if avg_latency <= 33.33:
                conclusions.append(f"✅ 平均延迟{avg_latency:.1f}ms，满足低延迟要求（≤33.33ms）")
            else:
                conclusions.append(f"⚠️ 平均延迟{avg_latency:.1f}ms，超过了低延迟要求（≤33.33ms）")

        for conclusion in conclusions:
            report.append(f"- {conclusion}")

        return "\n".join(report)

    def _generate_performance_plots(self):
        """生成性能图表"""
        self.logger.info("📊 生成性能图表...")

        # 训练曲线图
        if 'history' in self.training_metrics:
            self._plot_training_curves()

        # 推理性能分布图
        if self.inference_stats['fps_values']:
            self._plot_inference_performance()

        # 对比图表
        self._plot_performance_comparison()

    def _plot_training_curves(self):
        """绘制训练曲线"""
        history = self.training_metrics['history']
        epochs = list(range(len(history['losses'])))

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('HVI-RF-DETR训练性能曲线', fontsize=16, fontweight='bold')

        # Loss曲线
        ax1.plot(epochs, history['losses'], 'b-', linewidth=2, label='Training Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.set_title('训练损失曲线')
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # mAP曲线
        ax2.plot(epochs, [mAP * 100 for mAP in history['mAPs']], 'g-', linewidth=2, label='mAP')
        ax2.axhline(y=50, color='r', linestyle='--', alpha=0.7, label='目标阈值 (50%)')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('mAP (%)')
        ax2.set_title('检测精度曲线')
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        # FPS曲线
        ax3.plot(epochs, history['fps_values'], 'orange', linewidth=2, label='Training FPS')
        ax3.axhline(y=30, color='r', linestyle='--', alpha=0.7, label='目标阈值 (30 FPS)')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('FPS')
        ax3.set_title('训练速度曲线')
        ax3.grid(True, alpha=0.3)
        ax3.legend()

        # 学习率曲线
        if 'learning_rates' in history:
            ax4.plot(epochs, history['learning_rates'], 'purple', linewidth=2, label='Learning Rate')
            ax4.set_xlabel('Epoch')
            ax4.set_ylabel('Learning Rate')
            ax4.set_title('学习率调度曲线')
            ax4.set_yscale('log')
            ax4.grid(True, alpha=0.3)
            ax4.legend()

        plt.tight_layout()
        plt.savefig(self.paper_dir / 'training_curves.png', dpi=300, bbox_inches='tight')
        plt.close()

        self.logger.info("✅ 训练曲线图已保存")

    def _plot_inference_performance(self):
        """绘制推理性能分布图"""
        fps_values = self.inference_stats['fps_values']
        latency_values = self.inference_stats['latency_values']

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle('HVI-RF-DETR推理性能分布', fontsize=16, fontweight='bold')

        # FPS分布直方图
        ax1.hist(fps_values, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.axvline(x=np.mean(fps_values), color='red', linestyle='-', linewidth=2, label=f'平均值: {np.mean(fps_values):.1f}')
        ax1.axvline(x=30, color='orange', linestyle='--', linewidth=2, label='目标: 30 FPS')
        ax1.set_xlabel('FPS')
        ax1.set_ylabel('频次')
        ax1.set_title('推理FPS分布')
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # 延迟分布直方图
        ax2.hist(latency_values, bins=50, alpha=0.7, color='lightcoral', edgecolor='black')
        ax2.axvline(x=np.mean(latency_values), color='red', linestyle='-', linewidth=2, label=f'平均值: {np.mean(latency_values):.1f}ms')
        ax2.axvline(x=33.33, color='orange', linestyle='--', linewidth=2, label='目标: 33.33ms')
        ax2.set_xlabel('延迟 (ms)')
        ax2.set_ylabel('频次')
        ax2.set_title('推理延迟分布')
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        plt.tight_layout()
        plt.savefig(self.paper_dir / 'inference_performance.png', dpi=300, bbox_inches='tight')
        plt.close()

        self.logger.info("✅ 推理性能分布图已保存")

    def _plot_performance_comparison(self):
        """绘制性能对比图表"""
        comparison = self.experiment_report.get('comparison_analysis', {})
        if not comparison:
            return

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle('HVI-RF-DETR训练vs推理性能对比', fontsize=16, fontweight='bold')

        # FPS对比
        fps_comp = comparison.get('fps_comparison', {})
        if fps_comp:
            categories = ['训练FPS', '推理FPS']
            values = [fps_comp.get('training_fps', 0), fps_comp.get('inference_fps', 0)]
            colors = ['lightblue', 'lightgreen']

            bars = ax1.bar(categories, values, color=colors, alpha=0.8, edgecolor='black')
            ax1.axhline(y=30, color='red', linestyle='--', alpha=0.7, label='目标: 30 FPS')
            ax1.set_ylabel('FPS')
            ax1.set_title('FPS性能对比')
            ax1.grid(True, alpha=0.3, axis='y')
            ax1.legend()

            # 添加数值标签
            for bar, value in zip(bars, values):
                ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                        f'{value:.1f}', ha='center', va='bottom', fontweight='bold')

        # 目标达成情况
        target_achievement = comparison.get('target_achievement', {})
        if target_achievement:
            metrics = ['mAP', 'FPS', '延迟']
            achieved = [
                target_achievement.get('mAP_achievement_rate', 0),
                target_achievement.get('fps_achievement_rate', 0),
                100 if target_achievement.get('latency_achievement', False) else 0
            ]

            colors = ['green' if x >= 100 else 'orange' if x >= 80 else 'red' for x in achieved]

            bars = ax2.bar(metrics, achieved, color=colors, alpha=0.8, edgecolor='black')
            ax2.axhline(y=100, color='red', linestyle='--', alpha=0.7, label='目标: 100%')
            ax2.set_ylabel('达成率 (%)')
            ax2.set_title('目标达成情况')
            ax2.set_ylim(0, max(120, max(achieved) + 10))
            ax2.grid(True, alpha=0.3, axis='y')
            ax2.legend()

            # 添加数值标签
            for bar, value in zip(bars, achieved):
                ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 2,
                        f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()
        plt.savefig(self.paper_dir / 'performance_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()

        self.logger.info("✅ 性能对比图已保存")

    def _generate_latex_tables(self):
        """生成LaTeX表格"""
        self.logger.info("📝 生成LaTeX表格...")

        latex_content = []

        # 训练性能表格
        training_perf = self.experiment_report.get('training_performance', {})
        if training_perf:
            latex_content.append("% 训练性能表格")
            latex_content.append("\\begin{table}[htbp]")
            latex_content.append("\\centering")
            latex_content.append("\\caption{HVI-RF-DETR训练性能}")
            latex_content.append("\\label{tab:training_performance}")
            latex_content.append("\\begin{tabular}{|l|c|}")
            latex_content.append("\\hline")
            latex_content.append("指标 & 数值 \\\\")
            latex_content.append("\\hline")
            latex_content.append(f"最终mAP & {training_perf.get('final_mAP', 0):.3f} \\\\")
            latex_content.append(f"最佳mAP & {training_perf.get('best_mAP', 0):.3f} \\\\")
            latex_content.append(f"最终FPS & {training_perf.get('final_fps', 0):.1f} \\\\")
            latex_content.append(f"最佳FPS & {training_perf.get('best_fps', 0):.1f} \\\\")
            latex_content.append(f"最终Loss & {training_perf.get('final_loss', 0):.4f} \\\\")
            latex_content.append(f"训练轮数 & {training_perf.get('convergence_epochs', 0)} \\\\")
            latex_content.append("\\hline")
            latex_content.append("\\end{tabular}")
            latex_content.append("\\end{table}")
            latex_content.append("")

        # 推理性能表格
        inference_perf = self.experiment_report.get('inference_performance', {})
        if inference_perf:
            latex_content.append("% 推理性能表格")
            latex_content.append("\\begin{table}[htbp]")
            latex_content.append("\\centering")
            latex_content.append("\\caption{HVI-RF-DETR推理性能}")
            latex_content.append("\\label{tab:inference_performance}")
            latex_content.append("\\begin{tabular}{|l|c|}")
            latex_content.append("\\hline")
            latex_content.append("指标 & 数值 \\\\")
            latex_content.append("\\hline")
            latex_content.append(f"处理图片数 & {inference_perf.get('total_images_processed', 0):,} \\\\")
            latex_content.append(f"检测总数 & {inference_perf.get('total_detections', 0):,} \\\\")
            latex_content.append(f"平均FPS & {inference_perf.get('average_fps', 0):.1f} $\\pm$ {inference_perf.get('fps_std', 0):.1f} \\\\")
            latex_content.append(f"平均延迟(ms) & {inference_perf.get('average_latency_ms', 0):.1f} $\\pm$ {inference_perf.get('latency_std', 0):.1f} \\\\")
            latex_content.append(f"平均内存(MB) & {inference_perf.get('average_memory_mb', 0):.1f} \\\\")
            latex_content.append("\\hline")
            latex_content.append("\\end{tabular}")
            latex_content.append("\\end{table}")
            latex_content.append("")

        # 性能对比表格
        comparison = self.experiment_report.get('comparison_analysis', {})
        target_achievement = comparison.get('target_achievement', {})
        if target_achievement:
            latex_content.append("% 目标达成情况表格")
            latex_content.append("\\begin{table}[htbp]")
            latex_content.append("\\centering")
            latex_content.append("\\caption{HVI-RF-DETR目标达成情况}")
            latex_content.append("\\label{tab:target_achievement}")
            latex_content.append("\\begin{tabular}{|l|c|c|c|}")
            latex_content.append("\\hline")
            latex_content.append("指标 & 目标值 & 达成值 & 达成率 \\\\")
            latex_content.append("\\hline")
            latex_content.append(f"mAP & {target_achievement.get('mAP_target', 0):.1f}\\% & "
                               f"{target_achievement.get('mAP_achieved', 0):.3f} & "
                               f"{target_achievement.get('mAP_achievement_rate', 0):.1f}\\% \\\\")
            latex_content.append(f"FPS & {target_achievement.get('fps_target', 0):.1f} & "
                               f"{target_achievement.get('fps_achieved', 0):.1f} & "
                               f"{target_achievement.get('fps_achievement_rate', 0):.1f}\\% \\\\")
            latex_content.append(f"延迟(ms) & $\\leq${target_achievement.get('latency_target', 0):.1f} & "
                               f"{target_achievement.get('latency_achieved', 0):.1f} & "
                               f"{'达成' if target_achievement.get('latency_achievement', False) else '未达成'} \\\\")
            latex_content.append("\\hline")
            latex_content.append("\\end{tabular}")
            latex_content.append("\\end{table}")

        # 保存LaTeX文件
        latex_path = self.paper_dir / "experiment_tables.tex"
        with open(latex_path, 'w', encoding='utf-8') as f:
            f.write("\n".join(latex_content))

        self.logger.info(f"✅ LaTeX表格已生成: {latex_path}")

def main():
    """主函数 - 运行论文实验"""
    print("🎓 HVI-RF-DETR论文实验系统")
    print("=" * 50)

    try:
        # 创建配置
        config = PaperExperimentConfig()

        # 创建实验系统
        experiment_system = PaperExperimentSystem(config)

        # 运行论文实验
        experiment_system.run_paper_experiment()

        print("\n🎉 论文实验完成！")
        print(f"📁 实验结果保存在: {config.paper_output_dir}")
        print("📊 生成的文件:")
        print("   - experiment_report.md (论文实验报告)")
        print("   - experiment_data.json (详细实验数据)")
        print("   - training_curves.png (训练曲线图)")
        print("   - inference_performance.png (推理性能分布)")
        print("   - performance_comparison.png (性能对比图)")
        print("   - experiment_tables.tex (LaTeX表格)")

    except Exception as e:
        print(f"❌ 实验执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()