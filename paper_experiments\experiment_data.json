{
  "experiment_info": {
    "experiment_name": "HVI-RF-DETR夜间实时检测系统",
    "dataset": "BDD100K夜间子集",
    "model_architecture": "HVI-RF-DETR",
    "training_epochs": 50,
    "training_dataset_size": 28028,
    "batch_size": 32,
    "target_metrics": {
      "mAP_threshold": 0.5,
      "fps_threshold": 30.0,
      "latency_threshold": 33.33
    },
    "experiment_timestamp": "2025-06-24 23:55:01",
    "device": "cuda",
    "pytorch_version": "2.7.1+cu118"
  },
  "training_performance": {
    "final_mAP": 0.9316008363699531,
    "final_fps": 83.536291860683,
    "final_loss": 0.16727970423088448,
    "best_mAP": 0.9316008363699531,
    "best_fps": 83.536291860683,
    "convergence_epochs": 50,
    "total_training_iterations": 43800
  },
  "inference_performance": {
    "total_images_processed": 100000,
    "total_detections": 0,
    "average_fps": 72513.98620042959,
    "average_latency_ms": 0.021864066123962397,
    "average_memory_mb": 1447.38433546875,
    "fps_std": 32385.925297623064,
    "latency_std": 0.18953253921691768,
    "split_performance": {
      "train": {
        "images": 70000,
        "detections": 0,
        "avg_fps": 71918.98255992519,
        "avg_latency": 0.023945311137608116
      },
      "val": {
        "images": 10000,
        "detections": 0,
        "avg_fps": 78614.79323935637,
        "avg_latency": 0.013879990577697754
      },
      "test": {
        "images": 20000,
        "detections": 0,
        "avg_fps": 71546.0954227316,
        "avg_latency": 0.018571746349334717
      }
    }
  },
  "comparison_analysis": {
    "fps_comparison": {
      "training_fps": 83.536291860683,
      "inference_fps": 72513.98620042959,
      "fps_difference": 72430.4499085689,
      "fps_ratio": 868.0536876279382
    },
    "target_achievement": {
      "mAP_target": 0.5,
      "mAP_achieved": 0.9316008363699531,
      "mAP_achievement_rate": 186.32016727399062,
      "fps_target": 30.0,
      "fps_achieved": 72513.98620042959,
      "fps_achievement_rate": 241713.2873347653,
      "latency_target": 33.33,
      "latency_achieved": 0.021864066123962397,
      "latency_achievement": 