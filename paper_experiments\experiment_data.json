{
  "experiment_info": {
    "experiment_name": "HVI-RF-DETR夜间实时检测系统",
    "dataset": "BDD100K夜间子集",
    "model_architecture": "HVI-RF-DETR",
    "training_epochs": 50,
    "training_dataset_size": 28028,
    "batch_size": 32,
    "target_metrics": {
      "mAP_threshold": 0.5,
      "fps_threshold": 30.0,
      "latency_threshold": 33.33
    },
    "experiment_timestamp": "2025-06-24 23:21:42",
    "device": "cuda",
    "pytorch_version": "2.7.1+cu118"
  },
  "training_performance": {
    "final_mAP": 0.9316008363699531,
    "final_fps": 83.536291860683,
    "final_loss": 0.16727970423088448,
    "best_mAP": 0.9316008363699531,
    "best_fps": 83.536291860683,
    "convergence_epochs": 50,
    "total_training_iterations": 43800
  },
  "inference_performance": {
    "total_images_processed": 64936,
    "total_detections": 0,
    "average_fps": 629.379379578587,
    "average_latency_ms": 1.300466575395801,
    "average_memory_mb": 303.95941490708157,
    "fps_std": 914.622744934215,
    "latency_std": 1.5008454646103426,
    "split_performance": {
      "train": {
        "images": 64936,
        "detections": 0,
        "avg_fps": 629.3737002205886,
        "avg_latency": 1.3004711743286725
      }
    }
  },
  "comparison_analysis": {
    "fps_comparison": {
      "training_fps": 83.536291860683,
      "inference_fps": 629.379379578587,
      "fps_difference": 545.8430877179039,
      "fps_ratio": 7.534202986029468
    },
    "target_achievement": {
      "mAP_target": 0.5,
      "mAP_achieved": 0.9316008363699531,
      "mAP_achievement_rate": 186.32016727399062,
      "fps_target": 30.0,
      "fps_achieved": 629.379379578587,
      "fps_achievement_rate": 2097.9312652619565,
      "latency_target": 33.33,
      "latency_achieved": 1.300466575395801,
      "latency_achievement": 