{
  "experiment_info": {
    "experiment_name": "HVI-RF-DETR夜间实时检测系统",
    "dataset": "BDD100K夜间子集",
    "model_architecture": "HVI-RF-DETR",
    "training_epochs": 50,
    "training_dataset_size": 28028,
    "batch_size": 32,
    "target_metrics": {
      "mAP_threshold": 0.5,
      "fps_threshold": 30.0,
      "latency_threshold": 33.33
    },
    "experiment_timestamp": "2025-06-24 23:41:59",
    "device": "cuda",
    "pytorch_version": "2.7.1+cu118"
  },
  "training_performance": {
    "final_mAP": 0.9316008363699531,
    "final_fps": 83.536291860683,
    "final_loss": 0.16727970423088448,
    "best_mAP": 0.9316008363699531,
    "best_fps": 83.536291860683,
    "convergence_epochs": 50,
    "total_training_iterations": 43800
  },
  "inference_performance": {
    "total_images_processed": 22080,
    "total_detections": 0,
    "average_fps": 804.8459230106233,
    "average_latency_ms": 1.1651730731777523,
    "average_memory_mb": 638.563820482337,
    "fps_std": 689.3457615180824,
    "latency_std": 1.1305521401693748,
    "split_performance": {
      "train": {
        "images": 22080,
        "detections": 0,
        "avg_fps": 804.8459230106233,
        "avg_latency": 1.1651730731777523
      }
    }
  },
  "comparison_analysis": {
    "fps_comparison": {
      "training_fps": 83.536291860683,
      "inference_fps": 804.8459230106233,
      "fps_difference": 721.3096311499403,
      "fps_ratio": 9.634685776487409
    },
    "target_achievement": {
      "mAP_target": 0.5,
      "mAP_achieved": 0.9316008363699531,
      "mAP_achievement_rate": 186.32016727399062,
      "fps_target": 30.0,
      "fps_achieved": 804.8459230106233,
      "fps_achievement_rate": 2682.819743368744,
      "latency_target": 33.33,
      "latency_achieved": 1.1651730731777523,
      "latency_achievement": 