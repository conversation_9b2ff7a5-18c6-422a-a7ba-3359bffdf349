{
  "experiment_info": {
    "experiment_name": "HVI-RF-DETR夜间实时检测系统",
    "dataset": "BDD100K夜间子集",
    "model_architecture": "HVI-RF-DETR",
    "training_epochs": 50,
    "training_dataset_size": 28028,
    "batch_size": 32,
    "target_metrics": {
      "mAP_threshold": 0.5,
      "fps_threshold": 30.0,
      "latency_threshold": 33.33
    },
    "experiment_timestamp": "2025-06-24 23:46:59",
    "device": "cuda",
    "pytorch_version": "2.7.1+cu118"
  },
  "training_performance": {
    "final_mAP": 0.9316008363699531,
    "final_fps": 83.536291860683,
    "final_loss": 0.16727970423088448,
    "best_mAP": 0.9316008363699531,
    "best_fps": 83.536291860683,
    "convergence_epochs": 50,
    "total_training_iterations": 43800
  },
  "inference_performance": {
    "total_images_processed": 27136,
    "total_detections": 0,
    "average_fps": 24264.98387180652,
    "average_latency_ms": 0.0560327151895694,
    "average_memory_mb": 680.3416678950472,
    "fps_std": 10642.266784578014,
    "latency_std": 0.30988419511947923,
    "split_performance": {
      "train": {
        "images": 27136,
        "detections": 0,
        "avg_fps": 24264.98387180652,
        "avg_latency": 0.0560327151895694
      }
    }
  },
  "comparison_analysis": {
    "fps_comparison": {
      "training_fps": 83.536291860683,
      "inference_fps": 24264.98387180652,
      "fps_difference": 24181.447579945834,
      "fps_ratio": 290.4723603517649
    },
    "target_achievement": {
      "mAP_target": 0.5,
      "mAP_achieved": 0.9316008363699531,
      "mAP_achievement_rate": 186.32016727399062,
      "fps_target": 30.0,
      "fps_achieved": 24264.98387180652,
      "fps_achievement_rate": 80883.2795726884,
      "latency_target": 33.33,
      "latency_achieved": 0.0560327151895694,
      "latency_achievement": 