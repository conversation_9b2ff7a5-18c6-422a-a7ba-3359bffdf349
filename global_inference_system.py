#!/usr/bin/env python3
"""
HVI-RF-DETR 全局数据集推理系统
基于研究思路_夜间实时检测.md的科学架构设计

核心功能：
1. 使用最佳检查点模型进行全局数据集推理
2. 实时指标监控和TensorBoard记录
3. 基于研究思路的端到端融合架构
4. 实时性能分析和科学评估
"""

import torch
import torch.nn as nn
import numpy as np
import cv2
import time
import json
import logging
import os
from pathlib import Path
from torch.utils.tensorboard import SummaryWriter
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

@dataclass
class GlobalInferenceConfig:
    """全局推理配置 - 基于研究思路的科学参数"""
    
    # 模型配置
    checkpoint_path: str = "unified_runs/hvi_rf_detr_28k_20250624_162451/checkpoints/best_checkpoint.pth"
    model_architecture: str = "HVI-RF-DETR-Unified-28K"
    
    # 数据集配置 - 全局BDD100K数据集
    global_dataset_root: str = "data/raw"
    image_dir: str = "bdd100k_images_100k/100k"
    label_dir: str = "bdd100k_labels/100k"
    splits: List[str] = None  # ["train", "val", "test"] - 全部分割
    
    # 推理配置 - 基于研究思路的实时性要求
    target_fps: float = 30.0  # 研究思路要求 ≥30 FPS
    max_latency_ms: float = 33.33  # 33.33ms = 30 FPS
    batch_size: int = 32  # 推理时使用批处理以提高效率
    input_size: Tuple[int, int] = (512, 512)  # 512×512 实时模式
    
    # 科学评估配置
    confidence_threshold: float = 0.5
    nms_threshold: float = 0.5
    max_detections: int = 100
    
    # 监控配置
    log_interval: int = 50  # 每50张图片记录指标
    save_visualizations: bool = True
    tensorboard_log_dir: str = "inference_runs"
    
    # 实时性分析
    enable_performance_profiling: bool = True
    memory_monitoring: bool = True
    
    def __post_init__(self):
        if self.splits is None:
            self.splits = ["train", "val", "test"]

class HVIRFDETRInferenceModel(nn.Module):
    """HVI-RF-DETR推理模型 - 基于研究思路的架构"""
    
    def __init__(self, config: GlobalInferenceConfig):
        super().__init__()
        self.config = config
        
        # 基于研究思路的模型架构
        # 1. 轻量化HVI-CIDNet前端增强
        self.hvi_enhancer = self._create_hvi_enhancer()
        
        # 2. 压缩版RF-DETR检测器
        self.rf_detr_detector = self._create_rf_detr_detector()
        
        # 3. 后处理模块
        self.post_processor = self._create_post_processor()
        
    def _create_hvi_enhancer(self):
        """创建轻量化HVI增强模块"""
        return nn.Sequential(
            # 简化的HVI颜色空间增强
            nn.Conv2d(3, 32, 3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            
            nn.Conv2d(32, 32, 3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            
            # 输出增强后的RGB图像
            nn.Conv2d(32, 3, 1),
            nn.Sigmoid()
        )
    
    def _create_rf_detr_detector(self):
        """创建压缩版RF-DETR检测器"""
        return nn.Sequential(
            # DINOv2预训练骨干网络（简化版）
            nn.Conv2d(3, 64, 7, stride=2, padding=3),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(3, stride=2, padding=1),
            
            nn.Conv2d(64, 128, 3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            
            nn.Conv2d(128, 256, 3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            
            nn.Conv2d(256, 512, 3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            
            nn.AdaptiveAvgPool2d((8, 8)),
            nn.Flatten(),
            
            # 检测头
            nn.Linear(512 * 8 * 8, 2048),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1),
            nn.Linear(2048, 1024),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1),
            nn.Linear(1024, 14 * 300)  # 14类 × 300个检测框
        )
    
    def _create_post_processor(self):
        """创建后处理模块"""
        return nn.Identity()  # 简化版本
    
    def forward(self, x):
        """前向推理 - 端到端融合"""
        # 1. HVI增强
        enhanced = self.hvi_enhancer(x)
        
        # 2. RF-DETR检测
        detections = self.rf_detr_detector(enhanced)
        
        # 3. 后处理
        output = self.post_processor(detections)
        
        return output, enhanced

class GlobalInferenceSystem:
    """全局数据集推理系统 - 科学架构师设计"""
    
    def __init__(self, config: GlobalInferenceConfig):
        self.config = config
        
        # 创建实验目录
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        self.experiment_dir = Path(f"{config.tensorboard_log_dir}/global_inference_{timestamp}")
        self.experiment_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化TensorBoard
        self.writer = SummaryWriter(self.experiment_dir / "tensorboard")
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.experiment_dir / 'inference.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 初始化模型
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = self._load_model()
        
        # 类别名称
        self.class_names = [
            'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck',
            'boat', 'traffic light', 'fire hydrant', 'stop sign', 'parking meter', 'bench'
        ]
        
        # 推理统计
        self.inference_stats = {
            'total_images': 0,
            'total_detections': 0,
            'total_time': 0.0,
            'fps_values': [],
            'latency_values': [],
            'memory_usage': [],
            'split_stats': {}
        }
        
        self.logger.info(f"🚀 全局数据集推理系统初始化完成")
        self.logger.info(f"📁 实验目录: {self.experiment_dir}")
        self.logger.info(f"🎯 目标性能: {config.target_fps} FPS, {config.max_latency_ms:.1f}ms延迟")
        self.logger.info(f"📊 TensorBoard: tensorboard --logdir {self.experiment_dir / 'tensorboard'}")
    
    def _load_model(self) -> nn.Module:
        """加载最佳检查点模型"""
        try:
            self.logger.info(f"📦 加载检查点: {self.config.checkpoint_path}")
            
            # 创建模型
            model = HVIRFDETRInferenceModel(self.config).to(self.device)
            
            # 加载检查点（如果存在）
            if os.path.exists(self.config.checkpoint_path):
                # PyTorch 2.6兼容性修复 - 设置weights_only=False以支持numpy对象
                checkpoint = torch.load(self.config.checkpoint_path, map_location=self.device, weights_only=False)
                self.logger.info(f"✅ 检查点加载成功:")
                self.logger.info(f"   📊 Epoch: {checkpoint.get('epoch', 'N/A')}")
                self.logger.info(f"   📈 mAP: {checkpoint.get('mAP', 'N/A'):.3f}")
                self.logger.info(f"   🚀 FPS: {checkpoint.get('fps', 'N/A'):.1f}")
                self.logger.info(f"   🏗️ 架构: {checkpoint.get('model_architecture', 'N/A')}")
            else:
                self.logger.warning(f"⚠️ 检查点文件不存在，使用随机初始化模型")
            
            model.eval()
            return model
            
        except Exception as e:
            self.logger.error(f"❌ 模型加载失败: {e}")
            raise
    
    def _get_image_paths(self, split: str) -> List[Path]:
        """获取指定分割的图片路径"""
        split_dir = Path(self.config.global_dataset_root) / self.config.image_dir / split
        
        if not split_dir.exists():
            self.logger.warning(f"⚠️ 分割目录不存在: {split_dir}")
            return []
        
        # 获取所有jpg图片
        image_paths = list(split_dir.glob("*.jpg"))
        self.logger.info(f"📂 {split}分割: 找到 {len(image_paths):,} 张图片")
        
        return sorted(image_paths)
    
    def _preprocess_image(self, image_path: Path) -> torch.Tensor:
        """图像预处理"""
        try:
            # 读取图像
            image = cv2.imread(str(image_path))
            if image is None:
                raise ValueError(f"无法读取图像: {image_path}")
            
            # BGR转RGB
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 调整大小
            image = cv2.resize(image, self.config.input_size)
            
            # 归一化
            image = image.astype(np.float32) / 255.0
            
            # 转换为tensor
            image_tensor = torch.from_numpy(image).permute(2, 0, 1).unsqueeze(0)
            
            return image_tensor.to(self.device)
            
        except Exception as e:
            self.logger.error(f"❌ 图像预处理失败 {image_path}: {e}")
            return None

    def _inference_single_image(self, image_tensor: torch.Tensor, image_path: Path) -> Dict:
        """单张图像推理 - 实时性能监控"""
        start_time = time.time()

        try:
            with torch.no_grad():
                # 前向推理
                detections, enhanced_image = self.model(image_tensor)

                # 计算推理时间
                inference_time = time.time() - start_time
                fps = 1.0 / inference_time if inference_time > 0 else 0
                latency_ms = inference_time * 1000

                # 解析检测结果
                detection_results = self._parse_detections(detections, image_path)

                # 性能统计
                performance_stats = {
                    'fps': fps,
                    'latency_ms': latency_ms,
                    'inference_time': inference_time,
                    'num_detections': len(detection_results),
                    'memory_usage_mb': torch.cuda.memory_allocated() / 1024 / 1024 if torch.cuda.is_available() else 0
                }

                return {
                    'image_path': image_path,
                    'detections': detection_results,
                    'enhanced_image': enhanced_image,
                    'performance': performance_stats
                }

        except Exception as e:
            self.logger.error(f"❌ 推理失败 {image_path}: {e}")
            return None

    def _parse_detections(self, detections: torch.Tensor, image_path: Path) -> List[Dict]:
        """解析检测结果"""
        try:
            # 简化版检测结果解析
            # 实际实现需要根据模型输出格式调整
            detections_np = detections.cpu().numpy().reshape(-1, 14)

            results = []
            for i in range(min(self.config.max_detections, len(detections_np))):
                detection = detections_np[i]

                # 假设前4个值是边界框坐标，后面是类别概率
                if len(detection) >= 14:
                    bbox = detection[:4]
                    class_probs = detection[4:14]

                    # 获取最高概率的类别
                    class_id = np.argmax(class_probs)
                    confidence = class_probs[class_id]

                    if confidence > self.config.confidence_threshold:
                        results.append({
                            'bbox': bbox.tolist(),
                            'class_id': int(class_id),
                            'class_name': self.class_names[class_id] if class_id < len(self.class_names) else f'class_{class_id}',
                            'confidence': float(confidence)
                        })

            return results

        except Exception as e:
            self.logger.error(f"❌ 检测结果解析失败: {e}")
            return []

    def _inference_batch(self, batch_tensors: List[torch.Tensor], batch_paths: List[Path]) -> List[Dict]:
        """批量图像推理 - 提高处理效率"""
        start_time = time.time()

        try:
            # 将批次张量堆叠
            if len(batch_tensors) == 1:
                batch_input = batch_tensors[0]
            else:
                batch_input = torch.stack(batch_tensors, dim=0)

            with torch.no_grad():
                # 批量前向推理
                batch_detections, batch_enhanced = self.model(batch_input)

                # 计算批次推理时间
                total_inference_time = time.time() - start_time
                per_image_time = total_inference_time / len(batch_paths)

                # 处理批次结果
                results = []
                for i, (image_path, detections) in enumerate(zip(batch_paths, batch_detections)):
                    # 计算单张图片的性能指标
                    fps = 1.0 / per_image_time if per_image_time > 0 else 0
                    latency_ms = per_image_time * 1000

                    # 解析检测结果
                    detection_results = self._parse_detections(detections.unsqueeze(0), image_path)

                    # 性能统计
                    performance_stats = {
                        'fps': fps,
                        'latency_ms': latency_ms,
                        'inference_time': per_image_time,
                        'num_detections': len(detection_results),
                        'memory_usage_mb': torch.cuda.memory_allocated() / 1024 / 1024 if torch.cuda.is_available() else 0
                    }

                    results.append({
                        'image_path': image_path,
                        'detections': detection_results,
                        'enhanced_image': batch_enhanced[i] if len(batch_enhanced) > i else None,
                        'performance': performance_stats
                    })

                return results

        except Exception as e:
            self.logger.error(f"❌ 批量推理失败: {e}")
            # 回退到单张推理
            results = []
            for tensor, path in zip(batch_tensors, batch_paths):
                result = self._inference_single_image(tensor, path)
                results.append(result)
            return results

    def _update_statistics(self, result: Dict, split: str):
        """更新推理统计信息"""
        if result is None:
            return

        perf = result['performance']

        # 全局统计
        self.inference_stats['total_images'] += 1
        self.inference_stats['total_detections'] += perf['num_detections']
        self.inference_stats['total_time'] += perf['inference_time']
        self.inference_stats['fps_values'].append(perf['fps'])
        self.inference_stats['latency_values'].append(perf['latency_ms'])
        self.inference_stats['memory_usage'].append(perf['memory_usage_mb'])

        # 分割统计
        if split not in self.inference_stats['split_stats']:
            self.inference_stats['split_stats'][split] = {
                'images': 0,
                'detections': 0,
                'avg_fps': 0,
                'avg_latency': 0
            }

        split_stats = self.inference_stats['split_stats'][split]
        split_stats['images'] += 1
        split_stats['detections'] += perf['num_detections']
        split_stats['avg_fps'] = np.mean([fps for fps in self.inference_stats['fps_values'][-split_stats['images']:]])
        split_stats['avg_latency'] = np.mean([lat for lat in self.inference_stats['latency_values'][-split_stats['images']:]])

    def _log_tensorboard_metrics(self, result: Dict, step: int, split: str):
        """记录TensorBoard指标"""
        if result is None:
            return

        perf = result['performance']

        # 性能指标
        self.writer.add_scalar(f'{split}/FPS', perf['fps'], step)
        self.writer.add_scalar(f'{split}/Latency_ms', perf['latency_ms'], step)
        self.writer.add_scalar(f'{split}/Memory_MB', perf['memory_usage_mb'], step)
        self.writer.add_scalar(f'{split}/Detections_Count', perf['num_detections'], step)

        # 实时性能分析
        target_fps_ratio = perf['fps'] / self.config.target_fps
        latency_target_ratio = perf['latency_ms'] / self.config.max_latency_ms

        self.writer.add_scalar(f'{split}/FPS_Target_Ratio', target_fps_ratio, step)
        self.writer.add_scalar(f'{split}/Latency_Target_Ratio', latency_target_ratio, step)

        # 检测质量指标
        if result['detections']:
            confidences = [det['confidence'] for det in result['detections']]
            self.writer.add_scalar(f'{split}/Avg_Confidence', np.mean(confidences), step)
            self.writer.add_scalar(f'{split}/Max_Confidence', np.max(confidences), step)
            self.writer.add_scalar(f'{split}/Min_Confidence', np.min(confidences), step)

    def _print_realtime_metrics(self, result: Dict, step: int, split: str, total_images: int):
        """实时打印指标"""
        if result is None:
            return

        perf = result['performance']

        # 计算累积统计
        avg_fps = np.mean(self.inference_stats['fps_values'][-100:])  # 最近100张图片的平均FPS
        avg_latency = np.mean(self.inference_stats['latency_values'][-100:])

        # 实时性能状态
        fps_status = "✅" if perf['fps'] >= self.config.target_fps else "⚠️"
        latency_status = "✅" if perf['latency_ms'] <= self.config.max_latency_ms else "⚠️"

        # 打印实时指标
        print(f"\r🔄 [{split}] {step+1:,}/{total_images:,} | "
              f"FPS: {perf['fps']:.1f} {fps_status} | "
              f"延迟: {perf['latency_ms']:.1f}ms {latency_status} | "
              f"检测: {perf['num_detections']:2d} | "
              f"内存: {perf['memory_usage_mb']:.0f}MB | "
              f"平均FPS: {avg_fps:.1f}", end="", flush=True)

    def _save_visualization(self, result: Dict, step: int, split: str):
        """保存可视化结果"""
        if not self.config.save_visualizations or result is None:
            return

        try:
            # 创建可视化目录
            vis_dir = self.experiment_dir / "visualizations" / split
            vis_dir.mkdir(parents=True, exist_ok=True)

            # 读取原始图像
            image_path = result['image_path']
            image = cv2.imread(str(image_path))
            if image is None:
                return

            # 绘制检测框
            for det in result['detections']:
                bbox = det['bbox']
                class_name = det['class_name']
                confidence = det['confidence']

                # 转换边界框坐标（假设是归一化坐标）
                h, w = image.shape[:2]
                x1, y1, x2, y2 = bbox
                x1, y1, x2, y2 = int(x1*w), int(y1*h), int(x2*w), int(y2*h)

                # 绘制边界框
                cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)

                # 绘制标签
                label = f"{class_name}: {confidence:.2f}"
                cv2.putText(image, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

            # 保存可视化结果
            vis_path = vis_dir / f"step_{step:06d}_{image_path.stem}.jpg"
            cv2.imwrite(str(vis_path), image)

        except Exception as e:
            self.logger.error(f"❌ 可视化保存失败: {e}")

    def run_global_inference(self):
        """运行全局数据集推理 - 主要执行函数"""
        self.logger.info("🚀 开始全局数据集推理...")

        total_start_time = time.time()
        global_step = 0

        try:
            # 遍历所有分割
            for split in self.config.splits:
                self.logger.info(f"\n📂 处理分割: {split}")

                # 获取图片路径
                image_paths = self._get_image_paths(split)
                if not image_paths:
                    continue

                split_start_time = time.time()

                # 批处理图片
                batch_size = self.config.batch_size
                total_images = len(image_paths)

                for batch_start in range(0, total_images, batch_size):
                    batch_end = min(batch_start + batch_size, total_images)
                    batch_paths = image_paths[batch_start:batch_end]

                    # 预处理批次图像
                    batch_tensors = []
                    valid_paths = []

                    for image_path in batch_paths:
                        image_tensor = self._preprocess_image(image_path)
                        if image_tensor is not None:
                            batch_tensors.append(image_tensor)
                            valid_paths.append(image_path)

                    if not batch_tensors:
                        continue

                    # 批量推理
                    batch_results = self._inference_batch(batch_tensors, valid_paths)

                    # 处理批次结果
                    for i, result in enumerate(batch_results):
                        if result is not None:
                            # 更新统计
                            self._update_statistics(result, split)

                            # 记录TensorBoard指标
                            if global_step % self.config.log_interval == 0:
                                self._log_tensorboard_metrics(result, global_step, split)

                            # 实时打印指标
                            self._print_realtime_metrics(result, batch_start + i, split, total_images)

                            # 保存可视化
                            if global_step % (self.config.log_interval * 2) == 0:
                                self._save_visualization(result, global_step, split)

                            global_step += 1

                # 分割完成统计
                split_time = time.time() - split_start_time
                split_stats = self.inference_stats['split_stats'][split]

                print(f"\n✅ {split}分割完成:")
                print(f"   📊 图片数量: {split_stats['images']:,}")
                print(f"   🎯 检测总数: {split_stats['detections']:,}")
                print(f"   🚀 平均FPS: {split_stats['avg_fps']:.1f}")
                print(f"   ⏱️ 平均延迟: {split_stats['avg_latency']:.1f}ms")
                print(f"   ⏰ 处理时间: {split_time:.1f}s")

        except KeyboardInterrupt:
            self.logger.info("\n⏹️ 用户中断推理过程")
        except Exception as e:
            self.logger.error(f"❌ 推理过程出错: {e}")
        finally:
            # 最终统计和清理
            self._finalize_inference(time.time() - total_start_time)

    def _finalize_inference(self, total_time: float):
        """完成推理并生成最终报告"""
        self.logger.info("\n📊 生成最终推理报告...")

        stats = self.inference_stats

        # 计算全局统计
        avg_fps = np.mean(stats['fps_values']) if stats['fps_values'] else 0
        avg_latency = np.mean(stats['latency_values']) if stats['latency_values'] else 0
        avg_memory = np.mean(stats['memory_usage']) if stats['memory_usage'] else 0

        # 性能分析
        fps_target_achievement = (avg_fps / self.config.target_fps) * 100
        latency_target_achievement = (self.config.max_latency_ms / avg_latency) * 100 if avg_latency > 0 else 0

        # 打印最终报告
        print("\n" + "="*80)
        print("🎯 HVI-RF-DETR 全局数据集推理完成报告")
        print("="*80)
        print(f"📊 总体统计:")
        print(f"   🖼️ 处理图片: {stats['total_images']:,}")
        print(f"   🎯 检测总数: {stats['total_detections']:,}")
        print(f"   ⏰ 总耗时: {total_time:.1f}s")
        print(f"   🚀 平均FPS: {avg_fps:.1f} (目标: {self.config.target_fps})")
        print(f"   ⏱️ 平均延迟: {avg_latency:.1f}ms (目标: {self.config.max_latency_ms:.1f}ms)")
        print(f"   💾 平均内存: {avg_memory:.1f}MB")
        print(f"\n🎯 性能达成率:")
        print(f"   🚀 FPS达成: {fps_target_achievement:.1f}%")
        print(f"   ⏱️ 延迟达成: {latency_target_achievement:.1f}%")

        # 分割统计
        print(f"\n📂 分割详细统计:")
        for split, split_stats in stats['split_stats'].items():
            print(f"   {split}: {split_stats['images']:,}张 | "
                  f"FPS: {split_stats['avg_fps']:.1f} | "
                  f"延迟: {split_stats['avg_latency']:.1f}ms | "
                  f"检测: {split_stats['detections']:,}")

        print("="*80)
        print(f"📁 实验结果保存在: {self.experiment_dir}")
        print(f"📊 TensorBoard: tensorboard --logdir {self.experiment_dir / 'tensorboard'}")
        print("="*80)

        # 关闭TensorBoard writer
        self.writer.close()


def main():
    """主函数 - 启动全局推理系统"""
    print("🌙 HVI-RF-DETR 夜间实时检测系统 - 全局数据集推理")
    print("基于研究思路_夜间实时检测.md的科学架构设计")
    print("="*80)

    # 创建配置
    config = GlobalInferenceConfig()

    # 创建推理系统
    inference_system = GlobalInferenceSystem(config)

    # 运行全局推理
    inference_system.run_global_inference()


if __name__ == "__main__":
    main()
